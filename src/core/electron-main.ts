import { ipc<PERSON><PERSON>, <PERSON>rowserWindow, app, dialog, screen } from 'electron'
import { EventEmitter } from 'events'
import fs from 'fs/promises'
import fsSync from 'fs-extra'
import os from 'os'
import path from 'path'
import url from 'url'
import mime from 'mime-types'
import {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents,
	IPCRequest,
	IPCResponse,
	IPCEvent,
	SystemInfo,
	AppSettings,
	FileSelectOptions,
	DirectorySelectOptions,
	HttpRequestOptions,
	HttpResponse,
	WindowFactoryOptions,
	DownloadTaskOptions,
	IsUpdateAvailableOptions,
	IsUpdateAvailableResponse,
	DownloadEvent
} from '../method.types'
import { DEBUG, TEST } from '../env'
import { Downloader, DownloadOptions, DownloadProgress } from './downloader'

// 配置常量 (从 Configure.ts 转移)
export const MAIN_WINDOW_SIZE = { width: 1200, height: 800 }
export const APP_PATH = app.getPath('userData')
export const PACKAGE_PATH = path.join(APP_PATH, 'packages')
export const ASSETS_PATH = path.join(APP_PATH, 'appassets')
export const INSTALLED_META_FILE = 'lastest-installed-meta'
export const PACKAGE_META_FILE = 'lastest-downloaded-meta'
export const PROXY = 'myapp'
export const PROXY_UPDATER = 'updater'
export const WINDOW_ADAPTER: any = DEBUG
	? {
			'main-ui': 'http://localhost:5174',
			'main-ui#/liveroom-end/public-wall': 'http://localhost:5173#/liveroom-end/public-wall'
		}
	: {}

/**
 * Electron 主进程服务器
 * 负责处理来自渲染进程的IPC调用和事件管理
 */
export class ElectronMain extends EventEmitter {
	private apiHandlers: Partial<MainProcessAPI> = {}
	private eventListeners: Map<string, Set<(data: any) => void>> = new Map()
	private settings: AppSettings = {
		theme: 'auto',
		language: 'zh-CN',
		autoStart: false,
		notifications: true
	}

	// 窗口管理相关属性 (从 WindowFactory.ts 转移)
	private customWindows: Map<string, BrowserWindow> = new Map()
	private activeWindow: BrowserWindow | null = null
	private errorPage = '404 Not Found'

	// 更新器相关属性 (从 Updater.ts 转移)
	private updaters: Map<string, BrowserWindow> = new Map()
	private updateTask: Map<string, Downloader> = new Map()

	constructor() {
		super()
		this.setupIPC()
		this.registerDefaultHandlers()
		this.setupWindowFactory()
	}

	/**
	 * 设置IPC通信
	 */
	private setupIPC(): void {
		// 处理API调用
		ipcMain.handle('electron-api-invoke', async (event, request: IPCRequest) => {
			const response: IPCResponse = {
				id: request.id,
				success: false,
				timestamp: Date.now()
			}

			try {
				const handler = this.apiHandlers[request.method]
				if (!handler) {
					const error = new Error(`No handler found for method: ${request.method}`)
					console.error(error)
					return
				}

				const result = await handler.apply(
					this,
					Array.isArray(request.args) ? request.args : [request.args]
				)
				response.success = true
				response.data = result
			} catch (error: any) {
				response.error = {
					message: error.message,
					code: error.code,
					stack: error.stack
				}
			}

			return response
		})

		// 处理渲染进程事件
		ipcMain.on('electron-event-emit', (event, eventData: IPCEvent) => {
			super.emit(`renderer:${eventData.event}`, eventData.data)
		})
	}

	/**
	 * 注册默认的API处理器
	 */
	private registerDefaultHandlers(): void {
		this.registerAPI({
			// 系统信息相关
			getSystemInfo: async (): Promise<SystemInfo> => {
				return {
					platform: os.platform(),
					arch: os.arch(),
					version: os.release(),
					totalMemory: os.totalmem(),
					freeMemory: os.freemem(),
					cpuCount: os.cpus().length
				}
			},

			getAppVersion: async (): Promise<string> => {
				return app.getVersion()
			},

			// 窗口管理
			minimizeWindow: async (): Promise<void> => {
				const focusedWindow = BrowserWindow.getFocusedWindow()
				if (focusedWindow) {
					focusedWindow.minimize()
				}
			},

			maximizeWindow: async (): Promise<void> => {
				const focusedWindow = BrowserWindow.getFocusedWindow()
				if (focusedWindow) {
					if (focusedWindow.isMaximized()) {
						focusedWindow.unmaximize()
					} else {
						focusedWindow.maximize()
					}
				}
			},

			closeWindow: async (): Promise<void> => {
				const focusedWindow = BrowserWindow.getFocusedWindow()
				if (focusedWindow) {
					focusedWindow.close()
				}
			},

			// 文件操作
			selectFile: async (options?: FileSelectOptions): Promise<string | null> => {
				const result = await dialog.showOpenDialog({
					title: options?.title,
					defaultPath: options?.defaultPath,
					filters: options?.filters,
					properties: options?.properties || ['openFile']
				})

				return result.canceled ? null : result.filePaths[0]
			},

			selectDirectory: async (options?: DirectorySelectOptions): Promise<string | null> => {
				const result = await dialog.showOpenDialog({
					title: options?.title,
					defaultPath: options?.defaultPath,
					properties: options?.properties || ['openDirectory']
				})

				return result.canceled ? null : result.filePaths[0]
			},

			readFile: async (filePath: string): Promise<string> => {
				return await fs.readFile(filePath, 'utf-8')
			},

			writeFile: async (filePath: string, content: string): Promise<void> => {
				await fs.writeFile(filePath, content, 'utf-8')
			},

			// 应用设置
			getSettings: async (): Promise<AppSettings> => {
				return { ...this.settings }
			},

			updateSettings: async (newSettings: Partial<AppSettings>): Promise<void> => {
				this.settings = { ...this.settings, ...newSettings }
				// 这里可以添加持久化逻辑
				this.broadcast('settings-updated', this.settings)
			},

			// 网络请求
			httpRequest: async (options: HttpRequestOptions): Promise<HttpResponse> => {
				const { got } = await import('got')

				try {
					const response = await got(options.url, {
						method: options.method || 'GET',
						headers: options.headers,
						json: options.body,
						timeout: {
							request: options.timeout || 10000
						},
						responseType: 'text'
					})

					return {
						status: response.statusCode,
						statusText: response.statusMessage || '',
						headers: response.headers as Record<string, string>,
						data: this.tryParseJSON(response.body)
					}
				} catch (error: any) {
					// Handle Got errors
					if (error.response) {
						return {
							status: error.response.statusCode,
							statusText: error.response.statusMessage || '',
							headers: error.response.headers as Record<string, string>,
							data: this.tryParseJSON(error.response.body)
						}
					}
					throw error
				}
			},

			// 配置管理相关API
			getAppPath: async (): Promise<string> => {
				return APP_PATH
			},

			getPackagePath: async (): Promise<string> => {
				return PACKAGE_PATH
			},

			getAssetsPath: async (): Promise<string> => {
				return ASSETS_PATH
			},

			getWindowAdapter: async (): Promise<any> => {
				return WINDOW_ADAPTER
			},

			// 窗口工厂相关API
			openMainWindow: async (options: WindowFactoryOptions): Promise<string> => {
				return this.openMainWindow(options)
			},

			closeCustomWindow: async (windowId: string): Promise<void> => {
				return this.closeCustomWindow(windowId)
			},

			moveWindowToLeft: async (windowId?: string): Promise<void> => {
				return this.moveWindowToLeft(windowId)
			},

			moveWindowToRight: async (windowId?: string): Promise<void> => {
				return this.moveWindowToRight(windowId)
			},

			getActiveWindowId: async (): Promise<string | null> => {
				return this.getActiveWindowId()
			},

			// 更新器相关API
			startUpdater: async (dirname: string): Promise<string> => {
				return this.startUpdater(dirname)
			},

			closeUpdater: async (updaterId: string): Promise<void> => {
				return this.closeUpdater(updaterId)
			},

			getUpdaterWindow: async (updaterId: string): Promise<any> => {
				return this.getUpdaterWindow(updaterId)
			},

			// 浏览器代理功能API
			getTRTCSdkPath: async (): Promise<string> => {
				return this.getTRTCSdkPath()
			},

			getTRTCLogPath: async (): Promise<string> => {
				return this.getTRTCLogPath()
			},

			openDevTools: async (): Promise<void> => {
				return this.openDevTools()
			},

			// 包管理器功能API
			getLocalPackageVersion: async (pack: string): Promise<any> => {
				return this.getLocalPackageVersion(pack)
			},

			getServerPackageVersion: async (url: string): Promise<any> => {
				return this.getServerPackageVersion(url)
			},

			isUpdateAvailable: async (
				options: IsUpdateAvailableOptions
			): Promise<IsUpdateAvailableResponse> => {
				return this.isUpdateAvailable(options)
			},

			startDownloadTask: async (options: DownloadTaskOptions): Promise<any> => {
				return this.startDownloadTask(options)
			},

			abortDownloadTask: async (identity: string): Promise<void> => {
				return this.abortDownloadTask(identity)
			},

			decompressZip: async (options: { pack: string; file: string }): Promise<boolean> => {
				return this.decompressZip(options)
			}
		})
	}

	/**
	 * 注册API处理器
	 */
	public registerAPI(handlers: Partial<MainProcessAPI>): void {
		Object.assign(this.apiHandlers, handlers)
	}

	/**
	 * 向所有窗口广播事件
	 */
	public broadcast<K extends keyof MainProcessEvents>(
		event: K,
		data: MainProcessEvents[K]
	): void {
		const eventData: IPCEvent = {
			event: event as string,
			data,
			timestamp: Date.now()
		}

		BrowserWindow.getAllWindows().forEach((window) => {
			if (!window.isDestroyed()) {
				window.webContents.send('electron-event-broadcast', eventData)
			}
		})
	}

	/**
	 * 向特定窗口发送事件
	 */
	public sendToWindow<K extends keyof MainProcessEvents>(
		window: BrowserWindow,
		event: K,
		data: MainProcessEvents[K]
	): void {
		if (!window.isDestroyed()) {
			const eventData: IPCEvent = {
				event: event as string,
				data,
				timestamp: Date.now()
			}
			window.webContents.send('electron-event-broadcast', eventData)
		}
	}

	/**
	 * 监听渲染进程事件
	 */
	public onRendererEvent<K extends keyof RendererProcessEvents>(
		event: K,
		listener: (data: RendererProcessEvents[K]) => void
	): void {
		super.on(`renderer:${event}`, listener)
	}

	/**
	 * 尝试解析JSON
	 */
	private tryParseJSON(text: string): any {
		try {
			return JSON.parse(text)
		} catch {
			return text
		}
	}

	// ============================================================================
	// 事件管理功能 (从 Eventer.ts 转移)
	// ============================================================================

	/**
	 * 注册事件监听器 (兼容原有的 Eventer.register 方法)
	 */
	public register(event: string, handler: (message: any) => void): { unregister: () => void } {
		super.on(event, handler)

		return {
			unregister: () => {
				super.removeListener(event, handler)
			}
		}
	}

	/**
	 * 触发事件 (兼容原有的 Eventer.trigger 方法)
	 */
	public trigger(event: string, message: any): void {
		super.emit(event, message)
	}

	// ============================================================================
	// 窗口工厂功能 (从 WindowFactory.ts 转移)
	// ============================================================================

	/**
	 * 设置窗口工厂功能
	 */
	private setupWindowFactory(): void {
		// 注册代理通道处理器
		this.register('proxy-pass', ({ request, callback }: any) => {
			const location = url.parse(request.url)
			location.pathname = location.pathname || ''
			location.host = location.host || ''

			if (location.pathname === '/') {
				location.pathname = 'index.html'
			}

			if (/^\/__root__/.test(location.pathname)) {
				const parsed = location.pathname.match(/^\/__root__\/([^/]+?)\/(.+)/)
				if (parsed) {
					location.host = parsed[1]
					location.pathname = parsed[2]
				}
			}

			const file = path.join(ASSETS_PATH, location.host, location.pathname)
			const exist = fsSync.existsSync(file)
			const mimeType = mime.lookup(location.pathname!)

			if (exist) {
				try {
					const data = fsSync.readFileSync(file)
					callback({ mimeType, data })
					// eslint-disable-next-line @typescript-eslint/no-unused-vars
				} catch (e: any) {
					callback({ mimeType, data: Buffer.from(this.errorPage) })
				}
			} else {
				callback({ mimeType, data: Buffer.from(this.errorPage) })
			}
		})
	}

	/**
	 * 打开自定义窗口
	 */
	public openMainWindow(options: WindowFactoryOptions): string {
		const { pack, data = {}, unique = false } = options
		const windowId = `${pack}-${Date.now()}`

		if (unique) {
			BrowserWindow.getAllWindows().forEach((win: any) => {
				if (win.$$name$$ === pack) {
					win.close()
				}
			})
		}

		const { width, height } = data.size || MAIN_WINDOW_SIZE
		const screenSize = screen.getPrimaryDisplay().size
		let ratio = Math.min(screenSize.width / width, screenSize.height / height)
		ratio *= data.ratio || 1

		const window = new BrowserWindow({
			title: '豆神王者Club',
			width: (width * ratio) | 0,
			height: (height * ratio) | 0,
			resizable: true,
			center: true,
			frame: !data.noFrame,
			transparent: !!data.transparent,
			autoHideMenuBar: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false,
				preload: path.join(__dirname, '../preload/', 'index.js')
			}
		})

		// 设置用户代理
		const userAgent = window.webContents.getUserAgent()
		window.webContents.setUserAgent(`${userAgent} KCPC v${app.getVersion()} ${pack}`)

		// 确定加载的URL
		let loadUrl: string
		if (DEBUG && WINDOW_ADAPTER[pack]) {
			loadUrl = WINDOW_ADAPTER[pack]
		} else {
			loadUrl = `${PROXY}://${pack}${TEST || DEBUG ? '?env=test' : ''}`
		}

		if (data.remoteUrl) {
			loadUrl = data.remoteUrl
		}

		window.loadURL(loadUrl)

		if (DEBUG || TEST) {
			window.webContents.openDevTools()
		}

		// 设置窗口标识
		;(window as any).$$name$$ = pack
		;(window as any).$$id$$ = windowId

		// 存储窗口引用
		this.customWindows.set(windowId, window)
		this.activeWindow = window

		// 处理窗口关闭事件
		window.on('closed', () => {
			this.customWindows.delete(windowId)
			if (this.activeWindow === window) {
				this.activeWindow = null
			}
		})

		window.on('ready-to-show', () => {
			window.show()
		})

		return windowId
	}

	/**
	 * 关闭自定义窗口
	 */
	public closeCustomWindow(windowId: string): void {
		const window = this.customWindows.get(windowId)
		if (window && !window.isDestroyed()) {
			window.close()
		}
	}

	/**
	 * 将窗口移动到左侧
	 */
	public moveWindowToLeft(windowId?: string): void {
		const window = windowId ? this.customWindows.get(windowId) : this.activeWindow
		if (window && !window.isDestroyed()) {
			window.setPosition(0, 0)
		}
	}

	/**
	 * 将窗口移动到右侧
	 */
	public moveWindowToRight(windowId?: string): void {
		const window = windowId ? this.customWindows.get(windowId) : this.activeWindow
		if (window && !window.isDestroyed()) {
			const screenSize = screen.getPrimaryDisplay().size
			window.setPosition(screenSize.width - window.getSize()[0], 0)
		}
	}

	/**
	 * 获取活动窗口ID
	 */
	public getActiveWindowId(): string | null {
		if (!this.activeWindow || this.activeWindow.isDestroyed()) {
			return null
		}

		for (const [id, window] of this.customWindows.entries()) {
			if (window === this.activeWindow) {
				return id
			}
		}

		return null
	}

	// ============================================================================
	// 更新器功能 (从 Updater.ts 转移)
	// ============================================================================

	/**
	 * 启动更新器窗口
	 */
	public startUpdater(dirname: string): string {
		const updaterId = `updater-${Date.now()}`

		const updateWindow = new BrowserWindow({
			width: 600,
			height: 300,
			resizable: false,
			center: true,
			frame: false,
			autoHideMenuBar: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false
			}
		})

		updateWindow.on('closed', () => {
			this.updaters.delete(updaterId)
		})

		const url = DEBUG ? 'http://localhost:9007' : `updater://updater-dist`
		updateWindow.loadURL(url)

		if (DEBUG || TEST) {
			updateWindow.webContents.openDevTools()
		}

		// 存储更新器窗口引用
		this.updaters.set(updaterId, updateWindow)

		return updaterId
	}

	/**
	 * 关闭更新器窗口
	 */
	public closeUpdater(updaterId: string): void {
		const window = this.updaters.get(updaterId)
		if (window && !window.isDestroyed()) {
			window.close()
		}
	}

	/**
	 * 获取更新器窗口
	 */
	public getUpdaterWindow(updaterId: string): BrowserWindow | null {
		const window = this.updaters.get(updaterId)
		return window && !window.isDestroyed() ? window : null
	}

	// ============================================================================
	// 浏览器代理功能 (从 BrowserDelegates 转移)
	// ============================================================================

	/**
	 * 获取TRTC SDK路径 (从 info.ts 转移)
	 */
	public getTRTCSdkPath(): string {
		return path.join(ASSETS_PATH, 'trtc-electron-sdk')
	}

	/**
	 * 获取TRTC日志路径 (从 info.ts 转移)
	 */
	public getTRTCLogPath(): string {
		return path.join(ASSETS_PATH, 'trtc-electron-sdk', 'logs')
	}

	/**
	 * 打开开发者工具 (从 util.ts 转移)
	 */
	public openDevTools(): void {
		const focusedWindow = BrowserWindow.getFocusedWindow()
		if (focusedWindow) {
			focusedWindow.webContents.openDevTools()
		}
	}

	/**
	 * 获取本地包版本 (从 package-manager.ts 转移)
	 */
	public getLocalPackageVersion(pack: string): any {
		try {
			// const packagePath = path.join(ASSETS_PATH, pack, 'package.json')
			const packagePath = path.join(__dirname, '../../', pack, 'app.json')
			console.log('get local package version path: ', packagePath, pack)
			if (fsSync.existsSync(packagePath)) {
				const fileContent = fsSync.readFileSync(packagePath, 'utf8')
				console.log('fileContent: ', fileContent)
				const packageData = JSON.parse(fileContent)
				return packageData
			}
			return null
		} catch (error) {
			console.error('Error reading local package version:', error)
			return null
		}
	}

	/**
	 * 获取服务器包版本 (从 package-manager.ts 转移)
	 */
	public async getServerPackageVersion(url: string): Promise<any> {
		try {
			const { got } = await import('got')
			const response = await got(url, {
				responseType: 'json'
			})
			return response.body
		} catch (error) {
			console.error('Error fetching server package version:', error)
			return null
		}
	}

	/**
	 * 检查是否有更新可用
	 */
	public async isUpdateAvailable(options: {
		url: string
		pack: string
		checkVersionOnly?: boolean
	}): Promise<IsUpdateAvailableResponse> {
		try {
			const localVersion = this.getLocalPackageVersion(options.pack)
			const serverVersion = await this.getServerPackageVersion(options.url)

			if (!localVersion || !serverVersion) {
				return { hasUpdate: false, error: 'Version check failed' }
			}

			const hasUpdate = localVersion.version !== serverVersion.version

			return {
				hasUpdate,
				localVersion: localVersion.version,
				serverVersion: serverVersion.version,
				...(options.checkVersionOnly ? {} : serverVersion)
			}
		} catch (error) {
			console.error('Error checking for updates:', error)
			return { hasUpdate: false, error: error.message }
		}
	}

	/**
	 * 开始下载任务
	 */
	public async startDownloadTask(options: DownloadTaskOptions): Promise<void> {
		// 这里应该实现下载逻辑
		// 由于原始代码较复杂，这里提供一个简化版本
		console.log('Starting download task:', options)

		const cache_path = DEBUG
			? path.join(__dirname, '..', '..', 'downloader')
			: path.join(ASSETS_PATH, 'downloader')
		const packageName = `${options.pack}-${options.md5}.zip`

		const downloadOptions: DownloadOptions = {
			cache_path,
			md5: options.md5,
			package_name: packageName,
			taskId: options.taskId,
			url: options.url
		}
		const currentWindow = BrowserWindow.getFocusedWindow()
		const task = new Downloader(downloadOptions)
		task.on('error', (error) => {
			console.log('electron-main error download task:', error)
			const event: DownloadEvent = {
				event: 'error',
				data: error,
				taskId: task.taskId
			}
			currentWindow.webContents.send(`download-event`, event)
		})
		task.on('complete', () => {
			console.log('electron-main Completed download task:', currentWindow.id)
			const event: DownloadEvent = {
				data: undefined,
				taskId: task.taskId,
				event: 'complete'
			}
			currentWindow.webContents.send(`download-event`, event)
		})
		task.on('progress', (progress: DownloadProgress) => {
			console.log('electron-main progress download task:', progress, task.taskId)
			const event: DownloadEvent = {
				data: progress,
				taskId: task.taskId,
				event: 'progress'
			}
			currentWindow.webContents.send(`download-event`, event)
		})

		this.updateTask.set(options.taskId, task)
		setImmediate(() => {
			task.start()
		})

		return Promise.resolve()
		// return { success: true, message: 'Download task started' }
	}

	/**
	 * 中止下载任务 (从 package-manager.ts 转移)
	 */
	public async abortDownloadTask(taskId: string): Promise<void> {
		// 这里应该实现中止下载的逻辑
		console.log('Aborting download task:', taskId)
		const task = this.updateTask.get(taskId)
		if (!task) {
			return Promise.resolve()
		}
		task.cancel()
		this.updateTask.delete(taskId)
	}

	/**
	 * 解压ZIP文件 (从 package-manager.ts 转移)
	 */
	public async decompressZip(options: { pack: string; file: string }): Promise<boolean> {
		try {
			// 这里应该实现解压逻辑
			// 由于原始代码较复杂，这里提供一个简化版本
			console.log('Decompressing zip:', options)
			return true
		} catch (error) {
			console.error('Error decompressing zip:', error)
			return false
		}
	}
}

// 导出单例实例
export const electronMain = new ElectronMain()
